// Benchmark để so sánh 2 cách implement chunk

// Cách 1: Iterator thủ công (hiện tại)
function* chunkIterator<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = []
    
    for (let i = 0; i < size && !result.done; i++) {
      chunk.push(result.value)
      result = iterator.next()
    }
    
    if (chunk.length > 0) {
      yield chunk
    }
  }
}

// Cách 2: For...of loop
function* chunkForOf<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  let chunk: T[] = []

  for (const value of iterable) {
    chunk.push(value)

    if (chunk.length === size) {
      yield chunk
      chunk = []
    }
  }

  if (chunk.length > 0) {
    yield chunk
  }
}

// Hàm benchmark
function benchmark(name: string, fn: () => void, iterations: number = 1): number {
  const start = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    fn()
  }
  
  const end = performance.now()
  const totalTime = end - start
  const avgTime = totalTime / iterations
  
  console.log(`${name}: ${totalTime.toFixed(2)}ms total, ${avgTime.toFixed(4)}ms avg (${iterations} iterations)`)
  return totalTime
}

// Test data generators
function* largeGenerator(count: number) {
  for (let i = 0; i < count; i++) {
    yield i
  }
}

function createLargeArray(count: number): number[] {
  return Array.from({ length: count }, (_, i) => i)
}

console.log('=== CHUNK PERFORMANCE BENCHMARK ===\n')

// Test với array nhỏ
console.log('1. Small Array (10,000 items, chunk size 100):')
const smallArray = createLargeArray(10000)
const iterations1 = 1000

benchmark('Iterator method', () => {
  const chunks = Array.from(chunkIterator(smallArray, 100))
}, iterations1)

benchmark('For...of method', () => {
  const chunks = Array.from(chunkForOf(smallArray, 100))
}, iterations1)

console.log()

// Test với array lớn
console.log('2. Large Array (1,000,000 items, chunk size 1000):')
const largeArray = createLargeArray(1000000)
const iterations2 = 10

benchmark('Iterator method', () => {
  const chunks = Array.from(chunkIterator(largeArray, 1000))
}, iterations2)

benchmark('For...of method', () => {
  const chunks = Array.from(chunkForOf(largeArray, 1000))
}, iterations2)

console.log()

// Test với generator (lazy evaluation)
console.log('3. Generator (1,000,000 items, chunk size 1000):')
const iterations3 = 10

benchmark('Iterator method', () => {
  const chunks = Array.from(chunkIterator(largeGenerator(1000000), 1000))
}, iterations3)

benchmark('For...of method', () => {
  const chunks = Array.from(chunkForOf(largeGenerator(1000000), 1000))
}, iterations3)

console.log()

// Test với Set
console.log('4. Set (100,000 items, chunk size 100):')
const largeSet = new Set(createLargeArray(100000))
const iterations4 = 100

benchmark('Iterator method', () => {
  const chunks = Array.from(chunkIterator(largeSet, 100))
}, iterations4)

benchmark('For...of method', () => {
  const chunks = Array.from(chunkForOf(largeSet, 100))
}, iterations4)

console.log()

// Test memory usage với lazy evaluation
console.log('5. Memory Test - Processing first 5 chunks only (không load hết vào memory):')

console.log('Iterator method:')
console.time('Iterator - First 5 chunks')
let count1 = 0
for (const chunk of chunkIterator(largeGenerator(10000000), 10000)) {
  count1++
  if (count1 >= 5) break
}
console.timeEnd('Iterator - First 5 chunks')

console.log('For...of method:')
console.time('For...of - First 5 chunks')
let count2 = 0
for (const chunk of chunkForOf(largeGenerator(10000000), 10000)) {
  count2++
  if (count2 >= 5) break
}
console.timeEnd('For...of - First 5 chunks')

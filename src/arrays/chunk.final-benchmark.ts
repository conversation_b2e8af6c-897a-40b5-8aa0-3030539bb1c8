import { chunk } from './chunk'

// Old implementation for comparison
function* chunkOld<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = []
    
    for (let i = 0; i < size && !result.done; i++) {
      chunk.push(result.value)
      result = iterator.next()
    }
    
    if (chunk.length > 0) {
      yield chunk
    }
  }
}

function benchmark(name: string, fn: () => void, iterations: number = 1): number {
  const start = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    fn()
  }
  
  const end = performance.now()
  const totalTime = end - start
  
  console.log(`${name.padEnd(15)}: ${totalTime.toFixed(2)}ms`)
  return totalTime
}

function* largeGenerator(count: number) {
  for (let i = 0; i < count; i++) {
    yield i
  }
}

console.log('=== FINAL PERFORMANCE COMPARISON ===\n')

// Test 1: Array (should see massive improvement)
console.log('1. Array (1,000,000 items, chunk size 1000):')
const largeArray = Array.from({ length: 1000000 }, (_, i) => i)
const iterations1 = 10

const oldTime1 = benchmark('Old', () => Array.from(chunkOld(largeArray, 1000)), iterations1)
const newTime1 = benchmark('New', () => Array.from(chunk(largeArray, 1000)), iterations1)
const improvement1 = ((oldTime1 - newTime1) / oldTime1 * 100).toFixed(1)
console.log(`Improvement: ${improvement1}%\n`)

// Test 2: Generator (should see moderate improvement)
console.log('2. Generator (1,000,000 items, chunk size 1000):')
const iterations2 = 10

const oldTime2 = benchmark('Old', () => Array.from(chunkOld(largeGenerator(1000000), 1000)), iterations2)
const newTime2 = benchmark('New', () => Array.from(chunk(largeGenerator(1000000), 1000)), iterations2)
const improvement2 = ((oldTime2 - newTime2) / oldTime2 * 100).toFixed(1)
console.log(`Improvement: ${improvement2}%\n`)

// Test 3: Set (should see moderate improvement)
console.log('3. Set (100,000 items, chunk size 100):')
const largeSet = new Set(Array.from({ length: 100000 }, (_, i) => i))
const iterations3 = 50

const oldTime3 = benchmark('Old', () => Array.from(chunkOld(largeSet, 100)), iterations3)
const newTime3 = benchmark('New', () => Array.from(chunk(largeSet, 100)), iterations3)
const improvement3 = ((oldTime3 - newTime3) / oldTime3 * 100).toFixed(1)
console.log(`Improvement: ${improvement3}%\n`)

console.log('=== SUMMARY ===')
console.log(`Array improvement: ${improvement1}%`)
console.log(`Generator improvement: ${improvement2}%`)
console.log(`Set improvement: ${improvement3}%`)

// Verify correctness
console.log('\n=== CORRECTNESS VERIFICATION ===')
const testArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
const oldResult = Array.from(chunkOld(testArray, 3))
const newResult = Array.from(chunk(testArray, 3))
console.log('Old result:', JSON.stringify(oldResult))
console.log('New result:', JSON.stringify(newResult))
console.log('Results match:', JSON.stringify(oldResult) === JSON.stringify(newResult))

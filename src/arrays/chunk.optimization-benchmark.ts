import { 
  chunkCurrent, 
  chunkPreAlloc, 
  chunkFastPath, 
  chunkOptimized, 
  chunkUnrolled 
} from './chunk.optimized'

// Benchmark function
function benchmark(name: string, fn: () => void, iterations: number = 1): number {
  // Warm up
  for (let i = 0; i < Math.min(iterations, 10); i++) {
    fn()
  }

  const start = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    fn()
  }
  
  const end = performance.now()
  const totalTime = end - start
  const avgTime = totalTime / iterations
  
  console.log(`${name.padEnd(20)}: ${totalTime.toFixed(2)}ms total, ${avgTime.toFixed(4)}ms avg`)
  return totalTime
}

// Test data generators
function* largeGenerator(count: number) {
  for (let i = 0; i < count; i++) {
    yield i
  }
}

function createLargeArray(count: number): number[] {
  return Array.from({ length: count }, (_, i) => i)
}

console.log('=== CHUNK OPTIMIZATION BENCHMARK ===\n')

// Test 1: Small Array (best case for fast path)
console.log('1. Small Array (100,000 items, chunk size 100):')
const smallArray = createLargeArray(100000)
const iterations1 = 100

const results1: Record<string, number> = {}
results1['Current'] = benchmark('Current', () => Array.from(chunkCurrent(smallArray, 100)), iterations1)
results1['PreAlloc'] = benchmark('PreAlloc', () => Array.from(chunkPreAlloc(smallArray, 100)), iterations1)
results1['FastPath'] = benchmark('FastPath', () => Array.from(chunkFastPath(smallArray, 100)), iterations1)
results1['Optimized'] = benchmark('Optimized', () => Array.from(chunkOptimized(smallArray, 100)), iterations1)
results1['Unrolled'] = benchmark('Unrolled', () => Array.from(chunkUnrolled(smallArray, 100)), iterations1)

console.log('Improvement vs Current:')
Object.entries(results1).forEach(([name, time]) => {
  if (name !== 'Current') {
    const improvement = ((results1.Current - time) / results1.Current * 100).toFixed(1)
    console.log(`${name.padEnd(20)}: ${improvement}%`)
  }
})
console.log()

// Test 2: Large Array
console.log('2. Large Array (1,000,000 items, chunk size 1000):')
const largeArray = createLargeArray(1000000)
const iterations2 = 10

const results2: Record<string, number> = {}
results2['Current'] = benchmark('Current', () => Array.from(chunkCurrent(largeArray, 1000)), iterations2)
results2['PreAlloc'] = benchmark('PreAlloc', () => Array.from(chunkPreAlloc(largeArray, 1000)), iterations2)
results2['FastPath'] = benchmark('FastPath', () => Array.from(chunkFastPath(largeArray, 1000)), iterations2)
results2['Optimized'] = benchmark('Optimized', () => Array.from(chunkOptimized(largeArray, 1000)), iterations2)
results2['Unrolled'] = benchmark('Unrolled', () => Array.from(chunkUnrolled(largeArray, 1000)), iterations2)

console.log('Improvement vs Current:')
Object.entries(results2).forEach(([name, time]) => {
  if (name !== 'Current') {
    const improvement = ((results2.Current - time) / results2.Current * 100).toFixed(1)
    console.log(`${name.padEnd(20)}: ${improvement}%`)
  }
})
console.log()

// Test 3: Generator (no fast path available)
console.log('3. Generator (1,000,000 items, chunk size 1000):')
const iterations3 = 10

const results3: Record<string, number> = {}
results3['Current'] = benchmark('Current', () => Array.from(chunkCurrent(largeGenerator(1000000), 1000)), iterations3)
results3['PreAlloc'] = benchmark('PreAlloc', () => Array.from(chunkPreAlloc(largeGenerator(1000000), 1000)), iterations3)
results3['FastPath'] = benchmark('FastPath', () => Array.from(chunkFastPath(largeGenerator(1000000), 1000)), iterations3)
results3['Optimized'] = benchmark('Optimized', () => Array.from(chunkOptimized(largeGenerator(1000000), 1000)), iterations3)
results3['Unrolled'] = benchmark('Unrolled', () => Array.from(chunkUnrolled(largeGenerator(1000000), 1000)), iterations3)

console.log('Improvement vs Current:')
Object.entries(results3).forEach(([name, time]) => {
  if (name !== 'Current') {
    const improvement = ((results3.Current - time) / results3.Current * 100).toFixed(1)
    console.log(`${name.padEnd(20)}: ${improvement}%`)
  }
})
console.log()

// Test 4: Set (no fast path available)
console.log('4. Set (100,000 items, chunk size 100):')
const largeSet = new Set(createLargeArray(100000))
const iterations4 = 50

const results4: Record<string, number> = {}
results4['Current'] = benchmark('Current', () => Array.from(chunkCurrent(largeSet, 100)), iterations4)
results4['PreAlloc'] = benchmark('PreAlloc', () => Array.from(chunkPreAlloc(largeSet, 100)), iterations4)
results4['FastPath'] = benchmark('FastPath', () => Array.from(chunkFastPath(largeSet, 100)), iterations4)
results4['Optimized'] = benchmark('Optimized', () => Array.from(chunkOptimized(largeSet, 100)), iterations4)
results4['Unrolled'] = benchmark('Unrolled', () => Array.from(chunkUnrolled(largeSet, 100)), iterations4)

console.log('Improvement vs Current:')
Object.entries(results4).forEach(([name, time]) => {
  if (name !== 'Current') {
    const improvement = ((results4.Current - time) / results4.Current * 100).toFixed(1)
    console.log(`${name.padEnd(20)}: ${improvement}%`)
  }
})
console.log()

// Test 5: Small chunk sizes (good for unrolled)
console.log('5. Small chunks (100,000 items, chunk size 2):')
const iterations5 = 100

const results5: Record<string, number> = {}
results5['Current'] = benchmark('Current', () => Array.from(chunkCurrent(smallArray, 2)), iterations5)
results5['PreAlloc'] = benchmark('PreAlloc', () => Array.from(chunkPreAlloc(smallArray, 2)), iterations5)
results5['FastPath'] = benchmark('FastPath', () => Array.from(chunkFastPath(smallArray, 2)), iterations5)
results5['Optimized'] = benchmark('Optimized', () => Array.from(chunkOptimized(smallArray, 2)), iterations5)
results5['Unrolled'] = benchmark('Unrolled', () => Array.from(chunkUnrolled(smallArray, 2)), iterations5)

console.log('Improvement vs Current:')
Object.entries(results5).forEach(([name, time]) => {
  if (name !== 'Current') {
    const improvement = ((results5.Current - time) / results5.Current * 100).toFixed(1)
    console.log(`${name.padEnd(20)}: ${improvement}%`)
  }
})
console.log()

console.log('=== SUMMARY ===')
console.log('Best performers by test case:')
console.log('Small Array:', Object.entries(results1).reduce((a, b) => results1[a[0]] < results1[b[0]] ? a : b)[0])
console.log('Large Array:', Object.entries(results2).reduce((a, b) => results2[a[0]] < results2[b[0]] ? a : b)[0])
console.log('Generator:', Object.entries(results3).reduce((a, b) => results3[a[0]] < results3[b[0]] ? a : b)[0])
console.log('Set:', Object.entries(results4).reduce((a, b) => results4[a[0]] < results4[b[0]] ? a : b)[0])
console.log('Small chunks:', Object.entries(results5).reduce((a, b) => results5[a[0]] < results5[b[0]] ? a : b)[0])

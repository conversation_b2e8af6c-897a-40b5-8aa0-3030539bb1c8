// Các version tối ưu khác nhau để so sánh

// Version 1: Current implementation (baseline)
export function* chunkCurrent<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = []
    
    for (let i = 0; i < size && !result.done; i++) {
      chunk.push(result.value)
      result = iterator.next()
    }
    
    if (chunk.length > 0) {
      yield chunk
    }
  }
}

// Version 2: Pre-allocated array + index assignment
export function* chunkPreAlloc<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = new Array(size)
    let chunkIndex = 0
    
    while (chunkIndex < size && !result.done) {
      chunk[chunkIndex++] = result.value
      result = iterator.next()
    }
    
    if (chunkIndex > 0) {
      yield chunkIndex === size ? chunk : chunk.slice(0, chunkIndex)
    }
  }
}

// Version 3: Fast path for Arrays
export function* chunkFastPath<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  // Fast path for arrays
  if (Array.isArray(iterable)) {
    for (let i = 0; i < iterable.length; i += size) {
      yield iterable.slice(i, i + size)
    }
    return
  }

  // Fallback to iterator approach
  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = []
    
    for (let i = 0; i < size && !result.done; i++) {
      chunk.push(result.value)
      result = iterator.next()
    }
    
    if (chunk.length > 0) {
      yield chunk
    }
  }
}

// Version 4: Combined optimizations
export function* chunkOptimized<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  // Fast path for arrays
  if (Array.isArray(iterable)) {
    for (let i = 0; i < iterable.length; i += size) {
      yield iterable.slice(i, i + size)
    }
    return
  }

  // Optimized iterator approach
  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = new Array(size)
    let chunkIndex = 0
    
    while (chunkIndex < size && !result.done) {
      chunk[chunkIndex++] = result.value
      result = iterator.next()
    }
    
    if (chunkIndex > 0) {
      yield chunkIndex === size ? chunk : chunk.slice(0, chunkIndex)
    }
  }
}

// Version 5: Unrolled loop for small sizes (experimental)
export function* chunkUnrolled<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  // Unroll for common small sizes
  if (size <= 4) {
    while (!result.done) {
      const chunk: T[] = []
      
      // Unrolled loop
      if (!result.done) { chunk.push(result.value); result = iterator.next() }
      if (size > 1 && !result.done) { chunk.push(result.value); result = iterator.next() }
      if (size > 2 && !result.done) { chunk.push(result.value); result = iterator.next() }
      if (size > 3 && !result.done) { chunk.push(result.value); result = iterator.next() }
      
      if (chunk.length > 0) {
        yield chunk
      }
    }
    return
  }

  // Fallback for larger sizes
  while (!result.done) {
    const chunk: T[] = []
    
    for (let i = 0; i < size && !result.done; i++) {
      chunk.push(result.value)
      result = iterator.next()
    }
    
    if (chunk.length > 0) {
      yield chunk
    }
  }
}

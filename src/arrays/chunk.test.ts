import { chunk, chunkArray } from './chunk'

// Test với array nhỏ
console.log('Testing with small array:')
const smallArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
console.log('Original:', smallArray)
console.log('Chunks of 3:', Array.from(chunk(smallArray, 3)))
console.log('ChunkArray of 3:', chunkArray(smallArray, 3))

// Test với Set
console.log('\nTesting with Set:')
const testSet = new Set([1, 2, 3, 4, 5, 6, 7])
console.log('Set chunks of 2:', Array.from(chunk(testSet, 2)))

// Test với string (iterable)
console.log('\nTesting with string:')
const testString = 'Hello World'
console.log('String chunks of 3:', Array.from(chunk(testString, 3)))

// Test với generator function (iterable lớn)
function* largeIterable(count: number) {
  for (let i = 0; i < count; i++) {
    yield i
  }
}

console.log('\nTesting with large generator (first 5 chunks):')
const largeChunks = chunk(largeIterable(1000000), 1000)
let chunkCount = 0
for (const chunkItem of largeChunks) {
  console.log(`Chunk ${chunkCount + 1}: length=${chunkItem.length}, first=${chunkItem[0]}, last=${chunkItem[chunkItem.length - 1]}`)
  chunkCount++
  if (chunkCount >= 5) break // Chỉ hiển thị 5 chunk đầu
}

// Test edge cases
console.log('\nTesting edge cases:')
console.log('Empty array:', Array.from(chunk([], 3)))
console.log('Chunk size larger than array:', Array.from(chunk([1, 2], 5)))

// Test error case
try {
  Array.from(chunk([1, 2, 3], 0))
} catch (error) {
  console.log('Error for size 0:', (error as Error).message)
}

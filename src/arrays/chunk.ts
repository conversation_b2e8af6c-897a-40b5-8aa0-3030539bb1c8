/**
 * Chunks an iterable into smaller arrays of specified size
 * Uses generator for memory efficiency with large iterables
 */
export function* chunk<T>(iterable: Iterable<T>, size: number): Generator<T[], void, void> {
  if (size <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const iterator = iterable[Symbol.iterator]()
  let result = iterator.next()

  while (!result.done) {
    const chunk: T[] = []
    
    for (let i = 0; i < size && !result.done; i++) {
      chunk.push(result.value)
      result = iterator.next()
    }
    
    if (chunk.length > 0) {
      yield chunk
    }
  }
}

/**
 * Non-generator version that returns all chunks at once
 * Use with caution for large iterables as it loads everything into memory
 */
export function chunkArray<T>(iterable: Iterable<T>, size: number): T[][] {
  return Array.from(chunk(iterable, size))
}
